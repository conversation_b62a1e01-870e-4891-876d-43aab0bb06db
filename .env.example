# ------------------<PERSON><PERSON><PERSON> tracing------------------
LANGCHAIN_PROJECT="default"
LANGCHAIN_API_KEY=""
LANGCHAIN_TRACING_V2=true
# -----------------------------------------------------

# At least one of these must be set. Defaults to Azure OpenAI models. Ensure this is consistent
# with what is expected for the default models that are set in the GraphConfigPydantic class in tools_agent/agent.py

# Azure OpenAI Configuration (recommended)
AZURE_OPENAI_API_KEY=""
AZURE_OPENAI_ENDPOINT=""
AZURE_OPENAI_API_VERSION="2024-02-01"
OPENAI_API_VERSION="2024-02-01"

# Standard OpenAI Configuration (alternative)
OPENAI_API_KEY=""
ANTHROPIC_API_KEY=""

# For user level authentication
SUPABASE_URL=""
# Ensure this is your Supabase Service Role key
SUPABASE_KEY=""
